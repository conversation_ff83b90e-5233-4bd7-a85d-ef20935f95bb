{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["KjfGtB5/u7mKxUgslZQqm8WeHNxM+AOOkPY9lv0i+Nw=", "+e0VZaPTrm9uy7QpGP+9RVWPGfIYHSssUE46WWAhsdQ=", "NMLFhy08wyoziKUSEtK5MDoeEuHKrVTCZxuuN4H/l4Q=", "SKIzuJCxTmP/DH38Al/tTLL4cVBQZYrAm+ZrxNEH59M=", "FMva10UG9Sd57fmtUJ31BdWz/Eq7crX7XWavwB5cUKA=", "JPEoYjOtTYEW8lAhVagVfse8cftqUtrvfMhEld60qU8=", "haC0nnhEqcQqSRNU+N0OgtlpkBbD3q7GtKocKVKVTGs=", "ZNBGLcQrFiPBxpDd0unQ4wJ+qcsbEgz48m+0p3tlDbM=", "a+dxZjLAnBo4L4C2aNRktLQ6+gL+GE3PYdTsK8yHfv0=", "iPuGMpmLH00PFFec7lBe3LsQFT6YCscUbdKys17kxsY=", "HK0LVHTx3qB3Lt35BSDNPXeSlFTLAsVWwfU3sN11noY=", "higMbAwbeAebRxKkfhaGf2bMZR9xeWN04xY7RIP7rXg=", "WKvcYxI+Ddel2teQTL4wEbCxRF9+n8siNQyh08SRsSI=", "s7ZjB2KA1TCw8vSGmoJqGjNU6C1jQotalTz2153Cv1A=", "EiCXnNn6XIsvYDNuj6x+v1Pt9olKcddCx9O2GJQdR28=", "ekOaSeEB7O3w2cHb48I8/qGjAQ2zF9yqJ8VlrcghdZ0=", "4Kou4k22wyta479no5c6Ux4RKAN1TZki5VdvDsILA9A=", "7YO2/BRaSKakJ/lYVzcDFNz2RHKxHrToh409WesbLnQ=", "PohJREjxVSceRxcQzULEdboDahVbE+oAFbkyueV/L20=", "L1T7AUSV62+r3zbsvpNmefxdfwPvfiLcdF4njZ1ZpAc=", "tX0s4QhIg9TbIGJEak14PTsHky/+o4gD14i8C2oavJM=", "A5GpWYjBek3pVw/PERy0/tVjpWwkgexjAAQNry5eZ/s=", "KBJoCMGNh8uA3DgCs4H5KmsJcBDw2XJKKPOS9d2wwME=", "9DVoSoZH44xlpaWm1ZTalRZKMSx7UDutmA8rOTJg3OQ=", "dIMrr9JyQmBXd7CF1121y1N6DlWI6bChQGGaWyU1HXg=", "sGegetHsEaC/a97ResvzaOaOo6c9l6hfxaFHuikpi5k=", "sP/Rc1GqiakuLJPbN2n/KTPdwphJhTy80G4zjy+6p80=", "akBtN9bHWC6Dys+nPCTbvMlu4TNYHrsa3vDgQMBs5sU=", "QsLSPiHxdw7Zpjl44Mxek4/LZ/T8n+B3lV53wcv1ixo=", "eSqEyCmGgPvdWJ6E7aqDxgyjghUXkoQpfjYIcBUwJAY=", "mAWel0P7RVsG8Vn2ClX+4yN4htfpy+ox58grNF5sIW8=", "5yUjiq7BBDe9GVf+I9qDZ1tvY2Cvb/AXF4mJfHm3TEM=", "hNiAmfq8DGkSarwmliSSJpYFMNXDwambYO9vSdXSINE=", "jEHAkr2HWDwXdNpTDMccNhaFbJQX9BeIe5sSyXS2A1I=", "qp3LVyy/ALmUS/*****************************=", "uup1D50KAZJtniptrFzS5lw8na2BMXM4jG9oVXoC9aM=", "IQCxRCCfpkfx0c1eCHHR/qt2wJ/H5nsm65D84tMhjF4=", "vV29VptEXAuHGKspiCV5ePpN/lAOv6Q2i2gpJXzNKow=", "uHC7o/2MKKy2Ii7c298RFrInkrKCR5H6cQ8nmetR1PU=", "9b4i/JbKqn1slNw1Hz4J6JHGE/Ki+EDYe4X1deBRM/I=", "LyYzS5T2gzXU1OkqyF4/HATCOPWNla5Z20Om0e+EWKM=", "KiH27U6KALa99MOkn5sUochY9lMLHjVWXgNZnN2iB+I=", "xJIajltBls3G5mCBPaJXuSLx2dkDcilF4/T9qWYti+4=", "iAeoTt+L857MJcTUxW99JnDZgQixodCvqIPRf9d2dGE=", "lwdHdiS7jDg6xJPbQpi2BOjX/V3ZGafwmXH9BOXRjBw=", "3QPjkgxelhvwaJKKaGaQztJc+tEJ88H6r3BS7XtRaIE=", "m0XXm3Xty3AaAjDV9xewPfDE1OUjLWq/Mg9HnGm5Nn0=", "uOWsPe8t3LmXr0EZL4VbmkHxMjmntJWMw3Wa6nEBmy4=", "qfXe7fEeN9nD4pwJtuSPUprFOWq4Dy8iwqsWGgk6yho=", "IWHpDTFngdsdeL+0qgTE+T+zo5eB3dr/Y+OjHyBnwxw=", "1P9qKTCSkYfESzkK9BXORWWnvtzVkyJOi/TCjhj7j4w=", "IldG0ce2c9SXrhEqJUfSp/59MQNLEJhEIfBjPzssR0E=", "9nhd8Y4q0i63Dy9KvHTbz0K5eDOS+e3pkilphq6U98I=", "2J21k/U/SVKEWsfSdgl6JqL44sU13Tei3mOE/f/MFDw=", "g+qzo01YXTxOLuTRMnb4mRuNxEiI9fT9KgLrzAKt9sE=", "tciy1qFwhOLDusfkq+gE8BklTISnR6IZjVRH4bPQkG8=", "pjY8cklwErKWijKMdCYP27yFeQmhWN9OKPejxZg7sxU=", "kC/cPNk70aW3MLEbjgDrpOrMAqbSLiubCNoFXCRHsD4=", "1K4/Sw2aH26eAb1M0IDTfpx9BcimXx+U/ZKdhmOm9YM=", "FnPm3JbKGEuTgVTd8JVmN4uWK/J4aVuIXOzCdTRZYNY=", "8M9WvUdb3GfE7LBA0hBLNgPBicPW5BonwRE7djComL8=", "sUkVzTnCS7ZTi9W4J2+fyUITpxo1P4SYv7Nl+6kF8FI=", "7p7E2vFdYlhlSt08itCKUT6MEP3gzRUdoqRKh0ytOg0=", "N7GkamWBSt6d3NppByxONyhsVm7giFTSOCZnd1yNPcE=", "PlsAoSGRSHwvVEBh0j09LBTBtUoJivbfXuv24FxorK0=", "2+e6OtYGNCP4vb/SJtWZrGxgPcYxBByyTL55LWCzIoY=", "LcPfnmCTIRAOPT3z4+DnGuoPfZej8DoENtrM/znByms=", "OPpt+lih76ePecLKhrfbIkJu9m5KTXIHWPMroYq15Go=", "9Cmgbl6MbTg3TMMoEjsRkey74zXC3asIBICKpBOWXNk=", "njCuorWLMPjodzKw6Y8IBI+i9p3Tz4j7WOMCpZHtfbg=", "yTswfsfzjSM3LmCB/M9adOZ+gATfP94zaYA0vmlrR7o=", "7jDuwx5cCb7QjAMtPTqzsqAHXWX/A8rmymieiFHsfEM=", "6mqvYlqni5/XoPhoZdAec2yCi7ALsat0HmiWDmslbGk=", "CKAjjgF1osOrVRLNm2F7Z9iQb5BBp3vLWvcxd+Pk4Cc=", "98wZySRYqxfc+4ThduAR776r9+31KjeEgGwKXlUtsR4=", "2FS07Y7EZEflJFBfdLja0mbI1idOvjaFgLHxbqT4UwU=", "QwjB/NRGnXdKyZ73nShK9+KGFYinhGQon/WrMLBQ5sg=", "WbOLxjPG4LOci1N/gwu8/KnBI/7N4oOffNGtiVZHHbA=", "XT+1rVSuC7QOqKPPOrFbWebUX/2yCwjYqTdR1HN6uqc=", "xCHFPI5aJHqXvzehoIWhdgRS3RJA6W3j3lOaxjZwnp4=", "SNQmsdyS/hX3quwF4X+sDGFD6iaiRpc2yx2WaJGAI8k=", "BX9trPfNJ0yKVR0XpDgE4rKrbpUdccV2jahaFp7YKUE=", "F8r8Wzx3zPO/KnZ7a23nbKfW1mz+0/XTNRQbPnzKdXA=", "C+900XsgwhRphemL2yWrsfw9HXJJqOVkuQgK37MgFMI=", "R7DmqaiF+7dPQ3lNbSPgLV3fZUUdCgQmxdGQmp5VaM4=", "xrRGp0E0c82YAPBU40mA4qWJtwLf+sbrIEejWiI2AyM=", "PX6U1xQey2eypLq7ngn+tiIJlC/MNn4tBorYoH0m+hY=", "4S57kaPJW9F/43xi8i5d0EB4cYWPXQ4m9dsHWdZBEgA=", "fORDebX03PNjOWKpZHhq7/ZKL+xaLTaz4RkMF1sP9R8=", "OYOjLbRsmO0ogIFB7wUJksYn1pnQ/xhf7wXx1y0W4LI=", "zE5URJsQPb/Hla/up+Aex6pFgawZfxhpe4Iu+jGcxuI=", "+CgsRXWZeG1ATav1EiTdgD5v4RLM594qPdNkl6DJF34=", "xq5J9404gtziRpwTtqeLS4zh7IQsjIHwqeswstkhpOg=", "HagxD62vuhIj6euoiGs7mV6kVhN6zEHeojzdM8Ab9nY=", "fOtnlVFoP0qrr6NYkqFH7ldPRTkdruTTJNarzLlWzYA=", "tlbRLAkgmdLqy6bL4FuBJTqrKVbVhSAC/KULhsyw70Q=", "ZJ2zO+q4jPkfYSqH+VV+toEmi8cynzfKZc631Ch5guQ=", "iKsibc6tOQM6Fy0aXXuMAUI6ItNpxF3Gs2Boft7ElVA=", "12caQxvqHhIhtToxloJE++GBvLQEUjCg3cSnlDmG5J0=", "jH8oyoxD+XnSEuXtLv2IbadhQ48RIyQtXm77lY5tWmA=", "nu8a0B3laNBYCTK6F2N6/s0E8I6AWl7/N4+yHkNYb3w=", "USNge3OnSk/YTUJ07gp4g0DODMY2QxT941ubdDFl4AI=", "u4cGcEqs3JvJsGlRn/3MdC3Eo7D3kpbKbscrsSUr7jk=", "4MFMh5OmNpoYjjTebO6bufxvfNTPFQn5zyRnij8zBLI=", "OP3bJ/OFeRpeDM/T2ZuKxOhjNnhOiBtIeeiCDQMv3aY=", "KraweGUSmdqeoe8q2SIASpfXPIhkiE3Z2xAD5AjjVC0=", "PGGYuzH2+Yq8kPAroa02O+zGoWyADrnkJEmwShkzsPs=", "bqyPckfMV1qYVjK6lMsZRAMcW2u3xb6U6VRs2dYo1wc=", "fOF9+tERO5klE8l6hEjOL3WuetpbNx0J36bTHjFZ8RA=", "g2GJFMkiHa01VCApeU+9SLzVgmuvuE3ZnPdIzLNG5/o=", "qhEulHkp91x6Pe/ymJmYAY0Flsc7+UYV8H9rQ9ffECU=", "zjc8SACX1sCe5vVaj7EnuaHG8QsySWiIshri1pfqVOs=", "qUBkBfwUGeOwHdy4x9xLCWA10ddSSsUhCEel84g4D5Q=", "y2N82MYBV06TqIruDKqeTDvoJuAMe0wPRVGHsmPgVRc=", "ujIg8ptMhsHEvJqUBo8Ycr6UNfDYB/CvR80R1vy56nA=", "uD0ZG2u/m6z9kulqABqc+iDImaSlcP6Ngz6R88UlEJ4=", "FJvim8r8dqQS7gtaiQ/k4L0dhPiyAShUakUFQIBa/d4=", "G/e7s3Hob9DyBhxGuYzEZTrfixsQsfzd+Nb3FAtj8hU=", "47YkuhyQgpnoIk3z7Iezmko151vWLVJAp4pb96i8Tc8=", "ALDHr01iR5AzbP9lQTGJ1QrYCY3K5shPA3yql1gc5Cs=", "+ajDn2TPyzN5NKpkNNpzL0MsVmBSK4amV/2QcFWrEsw=", "02JRNFw76bk+p+pEM5XiQVqNEAu6gJ6c/B8+6Q5OdIc=", "Z2AYLhQmUIvg0bqhzHfr7ZTnaBaI+rAoJHFDbPQnWyk=", "BsqOxicogBtJs7QuOLRU+fmZRRORTjOOExrG6kLqRn4=", "l8pKbbujf7mN+rOMWc/tFaS+kJbQk1UNUsZHZwloWn8=", "F1aNhXDvp8Jrb2GQOMsttAXm+qmiV53aFmFxABAieUU="], "CachedAssets": {"KjfGtB5/u7mKxUgslZQqm8WeHNxM+AOOkPY9lv0i+Nw=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-07-19T18:23:24.5074163+00:00"}, "+e0VZaPTrm9uy7QpGP+9RVWPGfIYHSssUE46WWAhsdQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-07-19T18:23:24.5054141+00:00"}, "SKIzuJCxTmP/DH38Al/tTLL4cVBQZYrAm+ZrxNEH59M=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-19T18:23:24.5231446+00:00"}, "FMva10UG9Sd57fmtUJ31BdWz/Eq7crX7XWavwB5cUKA=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-19T18:23:24.5191424+00:00"}, "JPEoYjOtTYEW8lAhVagVfse8cftqUtrvfMhEld60qU8=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-19T18:23:24.5275887+00:00"}, "haC0nnhEqcQqSRNU+N0OgtlpkBbD3q7GtKocKVKVTGs=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-19T18:23:24.6069966+00:00"}, "ZNBGLcQrFiPBxpDd0unQ4wJ+qcsbEgz48m+0p3tlDbM=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-19T18:23:24.6109981+00:00"}, "a+dxZjLAnBo4L4C2aNRktLQ6+gL+GE3PYdTsK8yHfv0=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-19T18:23:24.6268229+00:00"}, "iPuGMpmLH00PFFec7lBe3LsQFT6YCscUbdKys17kxsY=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-19T18:23:24.6321879+00:00"}, "HK0LVHTx3qB3Lt35BSDNPXeSlFTLAsVWwfU3sN11noY=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-19T18:23:24.5335898+00:00"}, "higMbAwbeAebRxKkfhaGf2bMZR9xeWN04xY7RIP7rXg=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-19T18:23:24.5389091+00:00"}, "WKvcYxI+Ddel2teQTL4wEbCxRF9+n8siNQyh08SRsSI=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-19T18:23:24.5466437+00:00"}, "s7ZjB2KA1TCw8vSGmoJqGjNU6C1jQotalTz2153Cv1A=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-19T18:23:24.5506374+00:00"}, "EiCXnNn6XIsvYDNuj6x+v1Pt9olKcddCx9O2GJQdR28=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-19T18:23:24.559146+00:00"}, "ekOaSeEB7O3w2cHb48I8/qGjAQ2zF9yqJ8VlrcghdZ0=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-19T18:23:24.5623305+00:00"}, "4Kou4k22wyta479no5c6Ux4RKAN1TZki5VdvDsILA9A=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-19T18:23:24.6856834+00:00"}, "7YO2/BRaSKakJ/lYVzcDFNz2RHKxHrToh409WesbLnQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-19T18:23:24.6921597+00:00"}, "PohJREjxVSceRxcQzULEdboDahVbE+oAFbkyueV/L20=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-19T18:23:24.6981613+00:00"}, "L1T7AUSV62+r3zbsvpNmefxdfwPvfiLcdF4njZ1ZpAc=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-19T18:23:24.7033157+00:00"}, "tX0s4QhIg9TbIGJEak14PTsHky/+o4gD14i8C2oavJM=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-19T18:23:24.7216288+00:00"}, "A5GpWYjBek3pVw/PERy0/tVjpWwkgexjAAQNry5eZ/s=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-19T18:23:24.7299212+00:00"}, "KBJoCMGNh8uA3DgCs4H5KmsJcBDw2XJKKPOS9d2wwME=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-19T18:23:24.5747761+00:00"}, "9DVoSoZH44xlpaWm1ZTalRZKMSx7UDutmA8rOTJg3OQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-19T18:23:24.5807781+00:00"}, "dIMrr9JyQmBXd7CF1121y1N6DlWI6bChQGGaWyU1HXg=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-19T18:23:24.592971+00:00"}, "sGegetHsEaC/a97ResvzaOaOo6c9l6hfxaFHuikpi5k=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-19T18:23:24.599973+00:00"}, "sP/Rc1GqiakuLJPbN2n/KTPdwphJhTy80G4zjy+6p80=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-19T18:23:24.6784488+00:00"}, "akBtN9bHWC6Dys+nPCTbvMlu4TNYHrsa3vDgQMBs5sU=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-19T18:23:24.7658578+00:00"}, "QsLSPiHxdw7Zpjl44Mxek4/LZ/T8n+B3lV53wcv1ixo=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-19T18:23:24.7951344+00:00"}, "eSqEyCmGgPvdWJ6E7aqDxgyjghUXkoQpfjYIcBUwJAY=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-19T18:23:24.822795+00:00"}, "mAWel0P7RVsG8Vn2ClX+4yN4htfpy+ox58grNF5sIW8=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-19T18:23:24.8462414+00:00"}, "5yUjiq7BBDe9GVf+I9qDZ1tvY2Cvb/AXF4mJfHm3TEM=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-19T18:23:24.5797774+00:00"}, "hNiAmfq8DGkSarwmliSSJpYFMNXDwambYO9vSdXSINE=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-19T18:23:24.5767757+00:00"}, "jEHAkr2HWDwXdNpTDMccNhaFbJQX9BeIe5sSyXS2A1I=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-19T18:23:24.590251+00:00"}, "qp3LVyy/ALmUS/*****************************=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-19T18:23:24.6784488+00:00"}, "uup1D50KAZJtniptrFzS5lw8na2BMXM4jG9oVXoC9aM=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-19T18:23:24.7568574+00:00"}, "IQCxRCCfpkfx0c1eCHHR/qt2wJ/H5nsm65D84tMhjF4=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-19T18:23:24.7768397+00:00"}, "vV29VptEXAuHGKspiCV5ePpN/lAOv6Q2i2gpJXzNKow=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-19T18:23:24.6401905+00:00"}, "uHC7o/2MKKy2Ii7c298RFrInkrKCR5H6cQ8nmetR1PU=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-19T18:23:24.7434468+00:00"}, "9b4i/JbKqn1slNw1Hz4J6JHGE/Ki+EDYe4X1deBRM/I=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-19T18:23:24.7557816+00:00"}, "LyYzS5T2gzXU1OkqyF4/HATCOPWNla5Z20Om0e+EWKM=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-19T18:23:24.8103523+00:00"}, "KiH27U6KALa99MOkn5sUochY9lMLHjVWXgNZnN2iB+I=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-19T18:23:24.8900905+00:00"}, "xJIajltBls3G5mCBPaJXuSLx2dkDcilF4/T9qWYti+4=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-19T18:23:24.9004615+00:00"}, "iAeoTt+L857MJcTUxW99JnDZgQixodCvqIPRf9d2dGE=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-19T18:23:24.9128985+00:00"}, "lwdHdiS7jDg6xJPbQpi2BOjX/V3ZGafwmXH9BOXRjBw=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-19T18:23:24.9407357+00:00"}, "3QPjkgxelhvwaJKKaGaQztJc+tEJ88H6r3BS7XtRaIE=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-19T18:23:24.6846828+00:00"}, "m0XXm3Xty3AaAjDV9xewPfDE1OUjLWq/Mg9HnGm5Nn0=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-19T18:23:24.6961605+00:00"}, "uOWsPe8t3LmXr0EZL4VbmkHxMjmntJWMw3Wa6nEBmy4=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-19T18:23:24.7033157+00:00"}, "qfXe7fEeN9nD4pwJtuSPUprFOWq4Dy8iwqsWGgk6yho=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-19T18:23:24.7146265+00:00"}, "IWHpDTFngdsdeL+0qgTE+T+zo5eB3dr/Y+OjHyBnwxw=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-19T18:23:24.7216288+00:00"}, "1P9qKTCSkYfESzkK9BXORWWnvtzVkyJOi/TCjhj7j4w=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-19T18:23:24.7279175+00:00"}, "IldG0ce2c9SXrhEqJUfSp/59MQNLEJhEIfBjPzssR0E=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-19T18:23:24.7322382+00:00"}, "9nhd8Y4q0i63Dy9KvHTbz0K5eDOS+e3pkilphq6U98I=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-19T18:23:24.7444487+00:00"}, "2J21k/U/SVKEWsfSdgl6JqL44sU13Tei3mOE/f/MFDw=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-19T18:23:24.7514458+00:00"}, "g+qzo01YXTxOLuTRMnb4mRuNxEiI9fT9KgLrzAKt9sE=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-19T18:23:24.7568574+00:00"}, "tciy1qFwhOLDusfkq+gE8BklTISnR6IZjVRH4bPQkG8=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-19T18:23:24.7816503+00:00"}, "pjY8cklwErKWijKMdCYP27yFeQmhWN9OKPejxZg7sxU=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-19T18:23:24.7900086+00:00"}, "kC/cPNk70aW3MLEbjgDrpOrMAqbSLiubCNoFXCRHsD4=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-19T18:23:24.8083524+00:00"}, "1K4/Sw2aH26eAb1M0IDTfpx9BcimXx+U/ZKdhmOm9YM=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-19T18:23:24.8574865+00:00"}, "FnPm3JbKGEuTgVTd8JVmN4uWK/J4aVuIXOzCdTRZYNY=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-19T18:23:24.863704+00:00"}, "8M9WvUdb3GfE7LBA0hBLNgPBicPW5BonwRE7djComL8=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-19T18:23:24.8747078+00:00"}, "sUkVzTnCS7ZTi9W4J2+fyUITpxo1P4SYv7Nl+6kF8FI=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-19T18:23:24.8777071+00:00"}, "7p7E2vFdYlhlSt08itCKUT6MEP3gzRUdoqRKh0ytOg0=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\dj2881k1b2-qa529ixwsh.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "css/site#[.{fingerprint=qa529ixwsh}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z6fpcojqgw", "Integrity": "K7VFXd38ppASSD1PH+v8oF8nBrm/N01kQ6de09qXfNc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\css\\site.css", "FileLength": 8177, "LastWriteTime": "2025-07-19T19:13:03.0102516+00:00"}, "BsqOxicogBtJs7QuOLRU+fmZRRORTjOOExrG6kLqRn4=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\n2ba4e8li9-mlv21k5csn.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-19T18:23:24.8135491+00:00"}, "N7GkamWBSt6d3NppByxONyhsVm7giFTSOCZnd1yNPcE=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\m1mt0no8v7-61n19gt1b8.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-19T18:23:24.5094164+00:00"}, "NMLFhy08wyoziKUSEtK5MDoeEuHKrVTCZxuuN4H/l4Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-19T18:23:24.5141446+00:00"}, "PlsAoSGRSHwvVEBh0j09LBTBtUoJivbfXuv24FxorK0=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\azrmud3x8b-xtxxf3hu2r.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-19T18:23:24.5116901+00:00"}, "2+e6OtYGNCP4vb/SJtWZrGxgPcYxBByyTL55LWCzIoY=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\95cqc3g0fx-bqjiyaj88i.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-19T18:23:24.5359081+00:00"}, "LcPfnmCTIRAOPT3z4+DnGuoPfZej8DoENtrM/znByms=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\63crkw9lqd-c2jlpeoesf.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-19T18:23:24.5255913+00:00"}, "OPpt+lih76ePecLKhrfbIkJu9m5KTXIHWPMroYq15Go=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\89rxgglgqv-erw9l3u2r3.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-19T18:23:24.5285884+00:00"}, "9Cmgbl6MbTg3TMMoEjsRkey74zXC3asIBICKpBOWXNk=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\njkfy0jcs9-aexeepp0ev.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-19T18:23:24.5349106+00:00"}, "njCuorWLMPjodzKw6Y8IBI+i9p3Tz4j7WOMCpZHtfbg=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\w7fw5zhm6c-d7shbmvgxk.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-19T18:23:24.5419092+00:00"}, "yTswfsfzjSM3LmCB/M9adOZ+gATfP94zaYA0vmlrR7o=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\enm8bet7a9-ausgxo2sd3.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-19T18:23:24.5486385+00:00"}, "7jDuwx5cCb7QjAMtPTqzsqAHXWX/A8rmymieiFHsfEM=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\dgm9v586gb-k8d9w2qqmf.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-19T18:23:24.555147+00:00"}, "6mqvYlqni5/XoPhoZdAec2yCi7ALsat0HmiWDmslbGk=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\sgmmv3mbgk-cosvhxvwiu.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-19T18:23:24.6173583+00:00"}, "CKAjjgF1osOrVRLNm2F7Z9iQb5BBp3vLWvcxd+Pk4Cc=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\3ewamp8wlr-ub07r2b239.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-19T18:23:24.6223569+00:00"}, "98wZySRYqxfc+4ThduAR776r9+31KjeEgGwKXlUtsR4=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\r032wci4vz-fvhpjtyr6v.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-19T18:23:24.6288226+00:00"}, "2FS07Y7EZEflJFBfdLja0mbI1idOvjaFgLHxbqT4UwU=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\b1z6sdf1so-b7pk76d08c.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-19T18:23:24.6321879+00:00"}, "QwjB/NRGnXdKyZ73nShK9+KGFYinhGQon/WrMLBQ5sg=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\c1jcslm5o8-fsbi9cje9m.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-19T18:23:24.609998+00:00"}, "WbOLxjPG4LOci1N/gwu8/KnBI/7N4oOffNGtiVZHHbA=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ve22os7n99-rzd6atqjts.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-19T18:23:24.619357+00:00"}, "XT+1rVSuC7QOqKPPOrFbWebUX/2yCwjYqTdR1HN6uqc=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\brbtw8cll6-ee0r1s7dh0.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-19T18:23:24.6278211+00:00"}, "xCHFPI5aJHqXvzehoIWhdgRS3RJA6W3j3lOaxjZwnp4=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\pql65up4y9-dxx9fxp4il.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-19T18:23:24.6318232+00:00"}, "SNQmsdyS/hX3quwF4X+sDGFD6iaiRpc2yx2WaJGAI8k=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\vq0omhigoi-jd9uben2k1.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-19T18:23:24.63919+00:00"}, "BX9trPfNJ0yKVR0XpDgE4rKrbpUdccV2jahaFp7YKUE=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\xlgx6agu47-khv3u5hwcm.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-19T18:23:24.6466988+00:00"}, "F8r8Wzx3zPO/KnZ7a23nbKfW1mz+0/XTNRQbPnzKdXA=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\asdzb1i8vf-r4e9w2rdcm.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-19T18:23:24.6568605+00:00"}, "C+900XsgwhRphemL2yWrsfw9HXJJqOVkuQgK37MgFMI=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\o3rh16dtbu-lcd1t2u6c8.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-19T18:23:24.6608569+00:00"}, "R7DmqaiF+7dPQ3lNbSPgLV3fZUUdCgQmxdGQmp5VaM4=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\w2ilq12tud-c2oey78nd0.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-19T18:23:24.6401905+00:00"}, "xrRGp0E0c82YAPBU40mA4qWJtwLf+sbrIEejWiI2AyM=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\0ke1uhj14c-tdbxkamptv.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-19T18:23:24.6457007+00:00"}, "PX6U1xQey2eypLq7ngn+tiIJlC/MNn4tBorYoH0m+hY=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\53eb89ku88-j5mq2jizvt.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-19T18:23:24.6523877+00:00"}, "4S57kaPJW9F/43xi8i5d0EB4cYWPXQ4m9dsHWdZBEgA=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\qirrv7664x-06098lyss8.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-19T18:23:24.6598575+00:00"}, "fORDebX03PNjOWKpZHhq7/ZKL+xaLTaz4RkMF1sP9R8=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\qtkus9k3am-nvvlpmu67g.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-19T18:23:24.7444487+00:00"}, "OYOjLbRsmO0ogIFB7wUJksYn1pnQ/xhf7wXx1y0W4LI=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\340s5hso2v-s35ty4nyc5.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-19T18:23:24.7598562+00:00"}, "zE5URJsQPb/Hla/up+Aex6pFgawZfxhpe4Iu+jGcxuI=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\8vpzws9nyy-pj5nd1wqec.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-19T18:23:24.7890089+00:00"}, "+CgsRXWZeG1ATav1EiTdgD5v4RLM594qPdNkl6DJF34=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ir5ywmy3db-46ein0sx1k.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-19T18:23:24.8175503+00:00"}, "xq5J9404gtziRpwTtqeLS4zh7IQsjIHwqeswstkhpOg=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\d7p1td08qk-v0zj4ognzu.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-19T18:23:24.7434468+00:00"}, "HagxD62vuhIj6euoiGs7mV6kVhN6zEHeojzdM8Ab9nY=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\hfqe4n8vwx-37tfw0ft22.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-19T18:23:24.5349106+00:00"}, "fOtnlVFoP0qrr6NYkqFH7ldPRTkdruTTJNarzLlWzYA=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\oz4dm1cloq-hrwsygsryq.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-19T18:23:24.5601469+00:00"}, "tlbRLAkgmdLqy6bL4FuBJTqrKVbVhSAC/KULhsyw70Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ttetacwn98-pk9g2wxc8p.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-19T18:23:24.6109981+00:00"}, "ZJ2zO+q4jPkfYSqH+VV+toEmi8cynzfKZc631Ch5guQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\gvmh3klurf-ft3s53vfgj.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-19T18:23:24.7033157+00:00"}, "iKsibc6tOQM6Fy0aXXuMAUI6ItNpxF3Gs2Boft7ElVA=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\s2r2173bh3-6cfz1n2cew.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-19T18:23:24.7176254+00:00"}, "12caQxvqHhIhtToxloJE++GBvLQEUjCg3cSnlDmG5J0=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\onqlacepr7-6pdc2jztkx.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-19T18:23:24.8063504+00:00"}, "jH8oyoxD+XnSEuXtLv2IbadhQ48RIyQtXm77lY5tWmA=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\kqr05g8xvw-493y06b0oq.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-19T18:23:24.8307972+00:00"}, "nu8a0B3laNBYCTK6F2N6/s0E8I6AWl7/N4+yHkNYb3w=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\urcblhkvo8-iovd86k7lj.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-19T18:23:24.7033157+00:00"}, "USNge3OnSk/YTUJ07gp4g0DODMY2QxT941ubdDFl4AI=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\veyx1xsr92-vr1egmr9el.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-19T18:23:24.7146265+00:00"}, "u4cGcEqs3JvJsGlRn/3MdC3Eo7D3kpbKbscrsSUr7jk=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\s897umqpwl-kbrnm935zg.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-19T18:23:24.7309186+00:00"}, "4MFMh5OmNpoYjjTebO6bufxvfNTPFQn5zyRnij8zBLI=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\8km6ea020z-jj8uyg4cgr.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-19T18:23:24.7352382+00:00"}, "OP3bJ/OFeRpeDM/T2ZuKxOhjNnhOiBtIeeiCDQMv3aY=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\dch5k5hzbb-y7v9cxd14o.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-19T18:23:24.8350283+00:00"}, "KraweGUSmdqeoe8q2SIASpfXPIhkiE3Z2xAD5AjjVC0=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\cur2bgt2n0-notf2xhcfb.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-19T18:23:24.8442394+00:00"}, "PGGYuzH2+Yq8kPAroa02O+zGoWyADrnkJEmwShkzsPs=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\wvdjqi8e6y-h1s4sie4z3.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-19T18:23:24.8594841+00:00"}, "bqyPckfMV1qYVjK6lMsZRAMcW2u3xb6U6VRs2dYo1wc=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\34z6si6vvi-63fj8s7r0e.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-19T18:23:24.8330283+00:00"}, "fOF9+tERO5klE8l6hEjOL3WuetpbNx0J36bTHjFZ8RA=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\dmgigzlqef-0j3bgjxly4.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-19T18:23:24.8657033+00:00"}, "g2GJFMkiHa01VCApeU+9SLzVgmuvuE3ZnPdIzLNG5/o=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\c82kq708ap-47otxtyo56.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-19T18:23:24.8717062+00:00"}, "qhEulHkp91x6Pe/ymJmYAY0Flsc7+UYV8H9rQ9ffECU=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\zbulofmtvk-4v8eqarkd7.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-19T18:23:24.8747078+00:00"}, "zjc8SACX1sCe5vVaj7EnuaHG8QsySWiIshri1pfqVOs=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ddlidatjss-356vix0kms.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-19T18:23:24.8777071+00:00"}, "qUBkBfwUGeOwHdy4x9xLCWA10ddSSsUhCEel84g4D5Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\rnjlobj6hx-83jwlth58m.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-19T18:23:24.8821965+00:00"}, "y2N82MYBV06TqIruDKqeTDvoJuAMe0wPRVGHsmPgVRc=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\d532bwysf7-mrlpezrjn3.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-19T18:23:24.8860889+00:00"}, "ujIg8ptMhsHEvJqUBo8Ycr6UNfDYB/CvR80R1vy56nA=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\dznolve5q5-lzl9nlhx6b.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-19T18:23:24.8934596+00:00"}, "uD0ZG2u/m6z9kulqABqc+iDImaSlcP6Ngz6R88UlEJ4=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\cjffkhr2pd-ag7o75518u.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-19T18:23:24.8974595+00:00"}, "FJvim8r8dqQS7gtaiQ/k4L0dhPiyAShUakUFQIBa/d4=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\c29njwia6r-x0q3zqp4vz.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-19T18:23:24.9004615+00:00"}, "G/e7s3Hob9DyBhxGuYzEZTrfixsQsfzd+Nb3FAtj8hU=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\n6n2xmictt-0i3buxo5is.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-19T18:23:24.9397354+00:00"}, "47YkuhyQgpnoIk3z7Iezmko151vWLVJAp4pb96i8Tc8=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\fli5hie7gi-o1o13a6vjx.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-19T18:23:24.977236+00:00"}, "ALDHr01iR5AzbP9lQTGJ1QrYCY3K5shPA3yql1gc5Cs=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\qdytaxc5jf-ttgo8qnofa.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-19T18:23:25.0161293+00:00"}, "+ajDn2TPyzN5NKpkNNpzL0MsVmBSK4amV/2QcFWrEsw=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\6a5nda9alj-2z0ns9nrw6.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-19T18:23:25.0898977+00:00"}, "02JRNFw76bk+p+pEM5XiQVqNEAu6gJ6c/B8+6Q5OdIc=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\75o6zz68du-muycvpuwrr.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-19T18:23:25.1847596+00:00"}, "Z2AYLhQmUIvg0bqhzHfr7ZTnaBaI+rAoJHFDbPQnWyk=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\pqjggk2s14-87fc7y1x7t.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-19T18:23:25.3169109+00:00"}, "l8pKbbujf7mN+rOMWc/tFaS+kJbQk1UNUsZHZwloWn8=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\l37rrdn9dn-re983pout9.gz", "SourceId": "Nexcord", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "Nexcord#[.{fingerprint=re983pout9}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Nexcord.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mmk5acerye", "Integrity": "Um5eb0Ok2kv/4xTMR3sWzq4/38g0Mr72LN6ceT495fA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Nexcord.styles.css", "FileLength": 539, "LastWriteTime": "2025-07-19T18:23:24.5623305+00:00"}, "F1aNhXDvp8Jrb2GQOMsttAXm+qmiV53aFmFxABAieUU=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\3hxgyqmuhp-re983pout9.gz", "SourceId": "Nexcord", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "Nexcord#[.{fingerprint=re983pout9}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Nexcord.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mmk5acerye", "Integrity": "Um5eb0Ok2kv/4xTMR3sWzq4/38g0Mr72LN6ceT495fA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Nexcord.bundle.scp.css", "FileLength": 539, "LastWriteTime": "2025-07-19T18:23:24.5653341+00:00"}}, "CachedCopyCandidates": {}}