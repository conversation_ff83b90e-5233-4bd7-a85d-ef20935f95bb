﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Nexcord</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/Nexcord.styles.css" asp-append-version="true" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- CLEAN TAB FUNCTIONALITY -->
    <script>
        console.log('✅ Clean tab functionality loaded');

        function initializeTabs() {
            console.log('🔧 Initializing tabs...');

            // Set up tab click handlers
            document.querySelectorAll('[data-tab]').forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    const tabName = this.getAttribute('data-tab');
                    console.log('📋 Tab clicked:', tabName);

                    // Remove active class from all tabs
                    document.querySelectorAll('[data-tab]').forEach(t => t.classList.remove('active'));

                    // Add active class to clicked tab
                    this.classList.add('active');

                    // Hide all content
                    document.querySelectorAll('.tab-content').forEach(content => {
                        content.style.display = 'none';
                        content.classList.remove('active');
                    });

                    // Show selected content
                    const targetContent = document.getElementById(tabName + '-content');
                    if (targetContent) {
                        targetContent.style.display = 'block';
                        targetContent.classList.add('active');
                        console.log('✅ Showing content for:', tabName);

                        // If it's pending tab, reinitialize the accept/decline buttons
                        if (tabName === 'pending') {
                            setTimeout(initializeFriendRequestButtons, 100);
                        }
                    }
                });
            });

            console.log('✅ Tab handlers initialized');
        }

        function initializeFriendRequestButtons() {
            console.log('🔧 Initializing friend request buttons...');

            // Remove existing handlers to avoid duplicates
            document.querySelectorAll('.accept-btn, .decline-btn').forEach(btn => {
                btn.replaceWith(btn.cloneNode(true));
            });

            // Accept buttons
            document.querySelectorAll('.accept-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    console.log('✅ Accept button clicked');
                    const friendshipId = this.getAttribute('data-friendship-id');
                    const requestItem = this.closest('.pending-request');

                    this.disabled = true;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

                    fetch('/Friend/AcceptRequest', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'friendshipId=' + friendshipId
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Get friend info from the request item
                            const friendName = requestItem.querySelector('.friend-name').textContent;
                            const friendStatus = requestItem.querySelector('.friend-status').textContent;
                            const avatarElement = requestItem.querySelector('.friend-avatar img, .friend-avatar .avatar-placeholder');

                            // Remove from pending list with animation
                            requestItem.style.transition = 'opacity 0.3s';
                            requestItem.style.opacity = '0';
                            setTimeout(() => {
                                requestItem.remove();

                                // Update pending count in sidebar
                                const pendingBadge = document.querySelector('[data-channel="pending"] .notification-badge');
                                if (pendingBadge) {
                                    const currentCount = parseInt(pendingBadge.textContent) - 1;
                                    if (currentCount <= 0) {
                                        pendingBadge.remove();
                                    } else {
                                        pendingBadge.textContent = currentCount;
                                    }
                                }

                                // Add to friends list
                                addToFriendsList(friendName, friendStatus, avatarElement);
                            }, 300);

                            console.log('✅ Friend request accepted and added to friends list');
                        } else {
                            this.disabled = false;
                            this.innerHTML = '<i class="fas fa-check"></i>';
                            console.error('❌ Accept failed:', data.message);
                        }
                    })
                    .catch(error => {
                        this.disabled = false;
                        this.innerHTML = '<i class="fas fa-check"></i>';
                        console.error('❌ Accept error:', error);
                    });
                });
            });

            // Function to add accepted friend to the friends list
            function addToFriendsList(friendName, friendStatus, avatarElement) {
                const allContent = document.getElementById('all-content');
                const onlineContent = document.getElementById('online-content');

                if (allContent) {
                    const friendsList = allContent.querySelector('.friends-list');
                    if (friendsList) {
                        // Create new friend item
                        const newFriendItem = document.createElement('div');
                        newFriendItem.className = 'friend-item';

                        // Clone avatar
                        const avatarDiv = document.createElement('div');
                        avatarDiv.className = 'friend-avatar';
                        if (avatarElement) {
                            avatarDiv.appendChild(avatarElement.cloneNode(true));
                        } else {
                            avatarDiv.innerHTML = '<div class="avatar-placeholder"><i class="fas fa-user"></i></div>';
                        }

                        // Create friend info
                        const friendInfo = document.createElement('div');
                        friendInfo.className = 'friend-info';
                        friendInfo.innerHTML = `
                            <div class="friend-name">${friendName}</div>
                            <div class="friend-status">Online</div>
                        `;

                        // Create friend actions
                        const friendActions = document.createElement('div');
                        friendActions.className = 'friend-actions';
                        friendActions.innerHTML = `
                            <button class="action-btn message-btn" title="Message">
                                <i class="fas fa-comment"></i>
                            </button>
                            <button class="action-btn more-btn" title="More">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                        `;

                        // Assemble the friend item
                        newFriendItem.appendChild(avatarDiv);
                        newFriendItem.appendChild(friendInfo);
                        newFriendItem.appendChild(friendActions);

                        // Add to the top of the friends list
                        friendsList.insertBefore(newFriendItem, friendsList.firstChild);

                        // Add a subtle highlight animation
                        newFriendItem.style.backgroundColor = 'rgba(88, 101, 242, 0.3)';
                        setTimeout(() => {
                            newFriendItem.style.transition = 'background-color 2s';
                            newFriendItem.style.backgroundColor = '';
                        }, 100);

                        console.log('✅ Friend added to friends list');
                    }
                }
            }

            // Decline buttons
            document.querySelectorAll('.decline-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    console.log('❌ Decline button clicked');
                    const friendshipId = this.getAttribute('data-friendship-id');
                    const requestItem = this.closest('.pending-request');

                    this.disabled = true;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

                    fetch('/Friend/DeclineRequest', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'friendshipId=' + friendshipId
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            requestItem.style.transition = 'opacity 0.3s';
                            requestItem.style.opacity = '0';
                            setTimeout(() => requestItem.remove(), 300);
                            console.log('✅ Friend request declined');
                        } else {
                            this.disabled = false;
                            this.innerHTML = '<i class="fas fa-times"></i>';
                            console.error('❌ Decline failed:', data.message);
                        }
                    })
                    .catch(error => {
                        this.disabled = false;
                        this.innerHTML = '<i class="fas fa-times"></i>';
                        console.error('❌ Decline error:', error);
                    });
                });
            });

            console.log('✅ Friend request buttons initialized');
        }

        // Initialize everything when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            initializeTabs();
            initializeFriendRequestButtons();
        });

        // Also initialize on window load as backup
        window.addEventListener('load', function() {
            setTimeout(() => {
                initializeTabs();
                initializeFriendRequestButtons();
            }, 500);
        });
    </script>
</head>
<body class="nexcord-body">
    <header>
        <nav class="navbar nexcord-navbar navbar-expand-sm">
            <div class="container-fluid">
                <a class="nexcord-brand" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-gamepad"></i>
                    NEXCORD
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nexcord-nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Index" && ViewContext.RouteData.Values["controller"]?.ToString() == "Home" ? "active" : "")" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home"></i> Home
                            </a>
                        </li>
                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            <li class="nav-item">
                                <a class="nexcord-nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Dashboard" ? "active" : "")" asp-area="" asp-controller="Home" asp-action="Dashboard">
                                    <i class="fas fa-tachometer-alt"></i> Dashboard
                                </a>
                            </li>
                        }
                    </ul>
                    <ul class="navbar-nav">
                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            <li class="nav-item dropdown">
                                <a class="nexcord-nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user"></i> @User.Identity.Name
                                </a>
                                <ul class="dropdown-menu nexcord-dropdown dropdown-menu-end">
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-cog"></i> Settings</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post">
                                            <button type="submit" class="dropdown-item">
                                                <i class="fas fa-sign-out-alt"></i> Logout
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nexcord-nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Login" ? "active" : "")" asp-controller="Account" asp-action="Login">
                                    <i class="fas fa-sign-in-alt"></i> Login
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nexcord-nav-link nexcord-btn-primary @(ViewContext.RouteData.Values["action"]?.ToString() == "Register" ? "active" : "")" asp-controller="Account" asp-action="Register">
                                    <i class="fas fa-user-plus"></i> Sign Up
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <main role="main" class="nexcord-main">
        @RenderBody()
    </main>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.1/signalr.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
