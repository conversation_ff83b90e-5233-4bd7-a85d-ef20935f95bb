{"GlobalPropertiesHash": "X8XtGpJnQF/Ub6E7f2O8PIIE7mBoQ0ZcEoTn/sTHBkw=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["GEQWfrBh2iOnhZWrA+pNcdiZoc56qxo9o4M0RceH31g=", "cFD50QYi/+hFlFZdeMRuaVH1MKvAjtebM9F9ELaczLA=", "5eFJ2FVa1IRjUI0A4z/7bCqnx5y8YOog4p0+7lk1Q0Y=", "UMQyF6sZY3DIvHA2E6wprALWRzojmFMDL0rab0pRMDM=", "L8dezMVKwkhao8T8vIShd8QpoJco2DSKpJFTdzXEq8A=", "bjOmdv+hKzTQGZxem94I7ien1oIWcX85gtkye7FCsjI=", "nEvEpSKRkEFFRA25jC9p7jdXc3qpMG+kSRfuxts7494=", "Q6TJcZvZ5c7u+h7otGuep0KP/BsB6trFig4neikb+sY=", "0TXBwJ14M1YRpBzCWW3se8wR1ItPgd+vS/xomZ25gcU=", "Uc29gnS4RQQrdgLs5r0cz0O2MbUKWStK5ArKOK8Z1J8=", "AppqHj30s8eJxtgx5CXyVV4NsfBzAxq3B7KJpJJcjjI=", "mERVAqGKQLEJiApgBaW4gyx+AQzYL7l4B8VqokyGKRE=", "1HXoB9jIebNy9smdqL4FFWXH+CVMDu50D+/diBDBYuI=", "i0GdqbyfdUowp8ig8NjPyCIJgv/MW/xcJxqXBPHxCDg=", "z6pKpReAU52NDgfhP4LNIPA5B2pUXE/+sCXoVrIIC1o=", "JcC//AJExiRyLervh2I6Gy9TGoeIy2HE4g4Xjh+VOVk=", "C5bH/VDFxjcBkSclkyf78Hunsj/ldgACNy7ODu4h6lM=", "iU27CAnmKmDCEITx8Dc0owaYUxQaOozT3bFOk7FJbBk=", "9E+J+NcS8uQYZyQ9avHYj9uZXJdS1SG6xI9bgM3iIsY=", "4Qd1qmjNGpHLu0a4KlcoLEdjYXvsLtGmHMZk2FfnSGw=", "CYh1sUtSG4xO2NKGyqMdePpL0aj1O9zcUOs5EbheFGA=", "oAXH8lE9CPakt/jbt+gk1GoPsSoWfGmBL0BCt3GY4Bo=", "/gl2wFosQC2q5xSbUk96Oecq4k0AMFok0O7AC9nZxI8=", "+OtdBmlk7Xv+EH3ZHcGfj4F0yjJEUtBAgxnoRPcs6r0=", "1vxlI7+R1W+PjjeMhGyMSXqymxC43BVF0GLA38bIf1Y=", "fnvnLVc/ZRNJzH9um8rXA50tB0XulG7gKwVjcb7qckM=", "9RhEzFZYvABIjxGkEJFKVdMF2ck9MNUjriFO0aBxIwE=", "nE/eFhPu8lFZ/9abI5VdGQ3Gw6u09PSzgp1iFudVlq4=", "PZXBZjHH50oSpx+w08ofNx235XdseV0O6KgEqN2AjpU=", "A4LsAVW55XS+ufAFFw//DSGuaCTGYnJ4O6voqVm3Sb0=", "BQ1Gs62Fyr5LVMeTixcnJnQo54kSIZ1rzRDSCsXzZsU=", "xdc0o8Ad8Iing6qpFDBMemadOINDvxlt6mQII3cO2CE=", "ZoaLL4avPC/iQeYXUQM88WM+g/ywULOoR05VbrV/i7w=", "c24FtRwSzuC952FFfGH3+la4QGfLIu0FOZU8mmhgs/I=", "8a2cmmEQUscmB9uEDagdIySxH3AYGPbxHeo/AX2u98Q=", "JK3Xk+0Ahs3uM+zwWUVVIyWDkSAnq8g1ejmLkRoQWyU=", "7bETavo8r1L1Qp9QNgIPv9gvhv5ebZJG+5nHuwwsAVQ=", "jN0s8L0FsKKrec8UlxIC3+W53P+lO89XC50lvKKBeqw=", "Cz5g7p5iSpiftwmZXUyIu5atc5m/YwRLeOUvSg/HNXk=", "4BoT8JZEDWdMUdqWKEds2YXq3OWYwPCHZFa/LuptlY4=", "HqK8lXMYS7yW0Dmzkof18tk7ESon02aL0GxjuJhIwcU=", "jHYI1t4sdo8hZIIBbjHl5y8szeYwdCjI9F0DyAJYljA=", "hUFInwBJDx4K+PyAlGmPuWmv6Egsa5y/BRLhSWgJQ4E=", "Qd5S0pDeRv+3n+aZtXZ3IvM/TnSoxRqY/yTOy0vkz8Q=", "mdw0ku1tVyqeHFr7H+dhbvPn64UFYiq09xzNo9v2Z4A=", "SQPdVPjt+MpG9O4N5XvYimuWoBZTCT8SWMeDayCnfms=", "oOAalpSY6NOezDa9TioEu31hxQlnkudxzIXR3heG9SU=", "zDFf9IhRFZPUt2sLpjzsmDjtx7qdga1joomZXNAHXjg=", "7PjKvF9Q4Sj2T/oajujmb86ZmAYGXlQV/2pFuTjytKY=", "8yTxjGe189edUtVoTY6Wdrg44l5i0+UC0vdbfH6f10M=", "bgMVhdKdxHyu5XYPj5BXCQkfw4u0XiFbo9lMnoXTnDM=", "vlxMm/Bob7YdSl0wCJaMAXvHfDPWvMzB9L1cBsVLaRs=", "fWxexysdu3+T7cIUklrgwhDK26oYXbiGi/9VWQYleHw=", "p5xh4toY/ACaTWpVkTRu5GdyPrsuymuxzYtHkyRJqwk=", "3yEYkhDPc3ghCqYwR+HM8t8tICBCSSUT59UuIivMw8s=", "/+oIek5M35waiJu3gEQKCw/k4DS/ECRXYl4TykKrYlg=", "VqF0659zqCiRL2Vx8WXfbd8YQKqFqFSkV0CljgVz6VY=", "8UBvq3HFhJylgl47EtgZYiMy5Dje/0mm8Q/MEirdYIQ=", "AQWofhizRO+AOjcPiH3LoyWcLAdUg2OYyr9IBldo2iU=", "X9smmMZpzc2cYMa27FsxI58AK8PJyjppLzYLnYhsHtI=", "hFp+rL2dslJpao23G6ZBOXplkIo5sgCtU4AhO/PFqbA=", "JwvNwO1vomUhSrJKlu210vcD8pPgIKvHKPwMmBxr+LE=", "nrv/ZO0Sa5QP++VCDq2AK75UjHzSlM4c+gUA8RKMKiM=", "UBV0KZd6j2zYVT8basho+Koh37S7RJWuQfbVYF8S6WQ=", "fqicYh6A/EITHxkInSFwMxdeNK4ySp8m9QOz4jjP0JA=", "EW6lp35R9sCDZaqB5ZCyRgYtAuWnZ77krPxunP+P8iw=", "E7XIVeJMlNDiRL2ev3Wzgs3noftBqu8WzlqOt4iuADM=", "CuOKYDVPrsH88mE1AzY1K+sOaqv4y51AKS3Ek766Sdg=", "1LFK+1BS1yXjnZm3giONq+o8O17KAR53jhAFwCSd9i8=", "XIMFNRz+/3Syj8jeZJ4et+UHmGBM05ENItB5ONNcHnw=", "zIha2ZTHs75ciEuhsn4IfaklU6iDNwDudRklGzsU9gk=", "VPcHOQKinJP8z3XxSdWMjxKIcEpKzMiVbr7JjDe59vc=", "9nnQHuSykXdfk0d10Q/Q8qeypCjraiMDNRJjkzrYuzs=", "OiOhyk498DUL3F1JZdUNKaWXNYRmg8S+L2p2gioXZEY=", "QgOcYxBY/hCSZZd4YiiZMK2b1Ol1XY2zOeokQNFGcJ0=", "ckx2nfAagkX0e4paEZRw9A7NKhrh1qtKbO79hTmRz44=", "gk2gchnOwsXBT5e3mPwmrs/skXhXO5UDWG6ejVhCCUo=", "F6Zq01ZkjPpLdvN+NvdY7O53fMOgxq597byquzcsTSs="], "CachedAssets": {"GEQWfrBh2iOnhZWrA+pNcdiZoc56qxo9o4M0RceH31g=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\css\\site.css", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qa529ixwsh", "Integrity": "UsgQXRSxExdZynp2cllyQ4jxJ85+Mzyf5cYX6dkzAc4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 56589, "LastWriteTime": "2025-07-19T19:10:12.9155357+00:00"}, "cFD50QYi/+hFlFZdeMRuaVH1MKvAjtebM9F9ELaczLA=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\favicon.ico", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-17T14:42:07.0198511+00:00"}, "5eFJ2FVa1IRjUI0A4z/7bCqnx5y8YOog4p0+7lk1Q0Y=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\js\\site.js", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-17T14:42:07.114268+00:00"}, "UMQyF6sZY3DIvHA2E6wprALWRzojmFMDL0rab0pRMDM=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-17T14:42:06.6813511+00:00"}, "L8dezMVKwkhao8T8vIShd8QpoJco2DSKpJFTdzXEq8A=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-17T14:42:06.6869637+00:00"}, "bjOmdv+hKzTQGZxem94I7ien1oIWcX85gtkye7FCsjI=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-17T14:42:06.6889666+00:00"}, "nEvEpSKRkEFFRA25jC9p7jdXc3qpMG+kSRfuxts7494=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-17T14:42:06.6909656+00:00"}, "Q6TJcZvZ5c7u+h7otGuep0KP/BsB6trFig4neikb+sY=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-17T14:42:06.6941097+00:00"}, "0TXBwJ14M1YRpBzCWW3se8wR1ItPgd+vS/xomZ25gcU=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-17T14:42:06.6981627+00:00"}, "Uc29gnS4RQQrdgLs5r0cz0O2MbUKWStK5ArKOK8Z1J8=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-17T14:42:06.7043939+00:00"}, "AppqHj30s8eJxtgx5CXyVV4NsfBzAxq3B7KJpJJcjjI=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-17T14:42:06.7081848+00:00"}, "mERVAqGKQLEJiApgBaW4gyx+AQzYL7l4B8VqokyGKRE=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-17T14:42:06.7091837+00:00"}, "1HXoB9jIebNy9smdqL4FFWXH+CVMDu50D+/diBDBYuI=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-17T14:42:06.7182388+00:00"}, "i0GdqbyfdUowp8ig8NjPyCIJgv/MW/xcJxqXBPHxCDg=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-17T14:42:06.7245711+00:00"}, "z6pKpReAU52NDgfhP4LNIPA5B2pUXE/+sCXoVrIIC1o=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-17T14:42:06.7265724+00:00"}, "JcC//AJExiRyLervh2I6Gy9TGoeIy2HE4g4Xjh+VOVk=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-17T14:42:06.729203+00:00"}, "C5bH/VDFxjcBkSclkyf78Hunsj/ldgACNy7ODu4h6lM=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-17T14:42:06.7312479+00:00"}, "iU27CAnmKmDCEITx8Dc0owaYUxQaOozT3bFOk7FJbBk=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-17T14:42:06.734374+00:00"}, "9E+J+NcS8uQYZyQ9avHYj9uZXJdS1SG6xI9bgM3iIsY=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-17T14:42:06.7376496+00:00"}, "4Qd1qmjNGpHLu0a4KlcoLEdjYXvsLtGmHMZk2FfnSGw=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-17T14:42:06.7394354+00:00"}, "CYh1sUtSG4xO2NKGyqMdePpL0aj1O9zcUOs5EbheFGA=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-17T14:42:06.7414337+00:00"}, "oAXH8lE9CPakt/jbt+gk1GoPsSoWfGmBL0BCt3GY4Bo=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-17T14:42:06.7434347+00:00"}, "/gl2wFosQC2q5xSbUk96Oecq4k0AMFok0O7AC9nZxI8=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-17T14:42:06.7454353+00:00"}, "+OtdBmlk7Xv+EH3ZHcGfj4F0yjJEUtBAgxnoRPcs6r0=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-17T14:42:06.7474358+00:00"}, "1vxlI7+R1W+PjjeMhGyMSXqymxC43BVF0GLA38bIf1Y=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-17T14:42:06.7534174+00:00"}, "fnvnLVc/ZRNJzH9um8rXA50tB0XulG7gKwVjcb7qckM=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-17T14:42:06.7549383+00:00"}, "9RhEzFZYvABIjxGkEJFKVdMF2ck9MNUjriFO0aBxIwE=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-17T14:42:06.7569411+00:00"}, "nE/eFhPu8lFZ/9abI5VdGQ3Gw6u09PSzgp1iFudVlq4=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-17T14:42:06.7589383+00:00"}, "PZXBZjHH50oSpx+w08ofNx235XdseV0O6KgEqN2AjpU=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-17T14:42:06.7639403+00:00"}, "A4LsAVW55XS+ufAFFw//DSGuaCTGYnJ4O6voqVm3Sb0=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-17T14:42:06.7720624+00:00"}, "BQ1Gs62Fyr5LVMeTixcnJnQo54kSIZ1rzRDSCsXzZsU=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-17T14:42:06.7772085+00:00"}, "xdc0o8Ad8Iing6qpFDBMemadOINDvxlt6mQII3cO2CE=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-17T14:42:06.7792089+00:00"}, "ZoaLL4avPC/iQeYXUQM88WM+g/ywULOoR05VbrV/i7w=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-17T14:42:06.7836491+00:00"}, "c24FtRwSzuC952FFfGH3+la4QGfLIu0FOZU8mmhgs/I=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-17T14:42:06.7872179+00:00"}, "8a2cmmEQUscmB9uEDagdIySxH3AYGPbxHeo/AX2u98Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-17T14:42:06.790218+00:00"}, "JK3Xk+0Ahs3uM+zwWUVVIyWDkSAnq8g1ejmLkRoQWyU=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-17T14:42:06.7922193+00:00"}, "7bETavo8r1L1Qp9QNgIPv9gvhv5ebZJG+5nHuwwsAVQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-17T14:42:06.8023614+00:00"}, "jN0s8L0FsKKrec8UlxIC3+W53P+lO89XC50lvKKBeqw=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-17T14:42:06.8055916+00:00"}, "Cz5g7p5iSpiftwmZXUyIu5atc5m/YwRLeOUvSg/HNXk=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-17T14:42:06.8085908+00:00"}, "4BoT8JZEDWdMUdqWKEds2YXq3OWYwPCHZFa/LuptlY4=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-17T14:42:06.8105918+00:00"}, "HqK8lXMYS7yW0Dmzkof18tk7ESon02aL0GxjuJhIwcU=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-17T14:42:06.8175642+00:00"}, "jHYI1t4sdo8hZIIBbjHl5y8szeYwdCjI9F0DyAJYljA=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-17T14:42:06.8205653+00:00"}, "hUFInwBJDx4K+PyAlGmPuWmv6Egsa5y/BRLhSWgJQ4E=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-17T14:42:06.8245662+00:00"}, "Qd5S0pDeRv+3n+aZtXZ3IvM/TnSoxRqY/yTOy0vkz8Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-17T14:42:06.8275666+00:00"}, "mdw0ku1tVyqeHFr7H+dhbvPn64UFYiq09xzNo9v2Z4A=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-17T14:42:06.8591846+00:00"}, "SQPdVPjt+MpG9O4N5XvYimuWoBZTCT8SWMeDayCnfms=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-17T14:42:06.8611829+00:00"}, "oOAalpSY6NOezDa9TioEu31hxQlnkudxzIXR3heG9SU=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-17T14:42:06.8641956+00:00"}, "zDFf9IhRFZPUt2sLpjzsmDjtx7qdga1joomZXNAHXjg=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-17T14:42:06.6931098+00:00"}, "7PjKvF9Q4Sj2T/oajujmb86ZmAYGXlQV/2pFuTjytKY=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-17T14:42:07.0376841+00:00"}, "8yTxjGe189edUtVoTY6Wdrg44l5i0+UC0vdbfH6f10M=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-17T14:42:07.0558954+00:00"}, "bgMVhdKdxHyu5XYPj5BXCQkfw4u0XiFbo9lMnoXTnDM=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-17T14:42:06.7053931+00:00"}, "vlxMm/Bob7YdSl0wCJaMAXvHfDPWvMzB9L1cBsVLaRs=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-17T14:42:07.0085434+00:00"}, "fWxexysdu3+T7cIUklrgwhDK26oYXbiGi/9VWQYleHw=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-17T14:42:07.0164848+00:00"}, "p5xh4toY/ACaTWpVkTRu5GdyPrsuymuxzYtHkyRJqwk=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-17T14:42:07.022229+00:00"}, "3yEYkhDPc3ghCqYwR+HM8t8tICBCSSUT59UuIivMw8s=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-17T14:42:07.0232306+00:00"}, "/+oIek5M35waiJu3gEQKCw/k4DS/ECRXYl4TykKrYlg=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-17T14:42:06.7023955+00:00"}, "VqF0659zqCiRL2Vx8WXfbd8YQKqFqFSkV0CljgVz6VY=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-17T14:42:06.8709339+00:00"}, "8UBvq3HFhJylgl47EtgZYiMy5Dje/0mm8Q/MEirdYIQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-17T14:42:06.8741089+00:00"}, "AQWofhizRO+AOjcPiH3LoyWcLAdUg2OYyr9IBldo2iU=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-17T14:42:06.8896483+00:00"}, "X9smmMZpzc2cYMa27FsxI58AK8PJyjppLzYLnYhsHtI=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-17T14:42:06.8916486+00:00"}, "hFp+rL2dslJpao23G6ZBOXplkIo5sgCtU4AhO/PFqbA=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-17T14:42:06.9303478+00:00"}, "JwvNwO1vomUhSrJKlu210vcD8pPgIKvHKPwMmBxr+LE=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-17T14:42:06.9740301+00:00"}, "nrv/ZO0Sa5QP++VCDq2AK75UjHzSlM4c+gUA8RKMKiM=": {"Identity": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\nexcord\\Nexcord\\wwwroot\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-17T14:42:06.6951105+00:00"}}, "CachedCopyCandidates": {}}